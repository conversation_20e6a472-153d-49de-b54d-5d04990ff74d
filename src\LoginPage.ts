import { Page } from '@playwright/test';
import testData from '../test-data/test-data.json';
import envConfig from '../env/dev.json';

export class LoginPage {
  readonly page: Page;
  
  // Locators (update these to match your application)
  private readonly companyField = '#company';
  private readonly usernameField = '#username';  // Replace with your actual selector
  private readonly passwordField = '#password';  // Replace with your actual selector
  private readonly loginButton = '#btnSubmit';   // Replace with your actual selector
  private readonly errorMessage = '.error-message'; // Replace with your actual selector
  private readonly successMessage = '.success-message'; // Replace with your actual selector

  constructor(page: Page) {
    this.page = page;
  }

  // Navigation
  async navigate() {
    const loginUrl = envConfig.baseUrl + testData.urls.login;
    await this.page.goto(loginUrl);
  }

  // Actions
  async enterCompany(companycode: string) {
    await this.page.fill(this.companyField, companycode);
  }
  
  async enterUsername(username: string) {
    await this.page.fill(this.username<PERSON>ield, username);
  }

  async enterPassword(password: string) {
    await this.page.fill(this.passwordField, password);
  }

  async clickLoginButton() {
    // Wait for the button to be visible and enabled
    await this.page.waitForSelector(this.loginButton, { timeout: 5000 });
    await this.page.click(this.loginButton);
  }

  // Combined actions using test data
  async loginWithValidUser() {
    await this.enterCompany(testData.users.validUser.company);
    await this.enterUsername(testData.users.validUser.username);
    await this.enterPassword(testData.users.validUser.password);
    await this.clickLoginButton();
  }

  async loginWithInvalidUser() {
    await this.enterCompany(testData.users.invalidUser.company);
    await this.enterUsername(testData.users.invalidUser.username);
    await this.enterPassword(testData.users.invalidUser.password);
    await this.clickLoginButton();
  }

  async loginWithEmptyCredentials() {
    await this.enterCompany(testData.users.emptyCredentials.company);
    await this.enterUsername(testData.users.emptyCredentials.username);
    await this.enterPassword(testData.users.emptyCredentials.password);
    await this.clickLoginButton();
  }

  async loginWithCustomCredentials(company: string, username: string, password: string) {
    await this.enterCompany(company);
    await this.enterUsername(username);
    await this.enterPassword(password);
    await this.clickLoginButton();
  }

  // Verifications
  async getErrorMessage() {
    return await this.page.textContent(this.errorMessage);
  }

  async isErrorMessageVisible() {
    return await this.page.isVisible(this.errorMessage);
  }

  async isLoginButtonEnabled() {
    return await this.page.isEnabled(this.loginButton);
  }

  // Wait for navigation after successful login
  async waitForSuccessfulLogin() {
    // Wait for navigation away from login page (successful login)
    await this.page.waitForURL(envConfig.baseUrl, { timeout: 10000 });
  }
}