import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import { TopicManagerPage } from '../src/TopicManagerPage';

test.describe('Topic Management', () => {
  
  test('Create a new topic end-to-end', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);
    
    // Step 1: Login
    console.log('🔐 Starting login process...');
    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    
    // Verify login success
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    console.log('✅ Login successful!');
    
    // Step 2: Create Topic
    console.log('📝 Starting topic creation...');
    
    // Generate unique topic data
    const timestamp = Date.now();
    const topicName = `TopicNew2025_${timestamp}`;
    const uniqueCode = `TOPIC_${timestamp}`;
    const description = `Automated topic created on ${new Date().toLocaleDateString()}`;
    
    await topicManager.createTopicComplete(topicName, uniqueCode, description);
    
    console.log('🎉 Topic creation test completed successfully!');
  });

  test('Create topic with custom data', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);
    
    // Login first
    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    
    // Create topic with specific data
    await topicManager.createTopicComplete(
      'CustomTopic_Test',
      'CUSTOM_UC_001',
      'This is a custom topic for testing purposes'
    );
  });

  test('Navigate to topic creation page only', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);
    
    // Login
    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    
    // Handle session confirmation
    try {
      await topicManager.handleSessionConfirmation();
    } catch (error) {
      console.log('ℹ️ No session confirmation needed');
    }
    
    // Just navigate to topic creation page
    await topicManager.navigateToTopicCreation();
    
    // Take a screenshot for verification
    await page.screenshot({ path: 'topic-creation-page.png', fullPage: true });
    
    console.log('📸 Screenshot taken of topic creation page');
  });
});
