import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import { TopicManagerPage } from '../src/TopicManagerPage';

test('Simple Topic Creation - Login and Create Topic', async ({ page }) => {
  // Initialize page objects
  const loginPage = new LoginPage(page);
  const topicManager = new TopicManagerPage(page);
  
  console.log('🚀 Starting automated topic creation...');
  
  // Step 1: Login
  console.log('🔐 Logging in...');
  await loginPage.navigate();
  await loginPage.loginWithValidUser();
  
  // Verify login
  await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
  console.log('✅ Login successful!');
  
  // Step 2: Handle session confirmation
  try {
    await topicManager.handleSessionConfirmation();
    console.log('✅ Session confirmed');
  } catch (error) {
    console.log('ℹ️ No session confirmation needed');
  }
  
  // Step 3: Navigate to topic creation
  console.log('🧭 Navigating to topic creation...');
  await topicManager.navigateToTopicCreation();
  
  // Step 4: Create topic with unique data
  const timestamp = Date.now();
  const topicData = {
    name: `AutoTopic_${timestamp}`,
    code: `AUTO_${timestamp}`,
    description: `Automated topic created at ${new Date().toLocaleString()}`
  };
  
  console.log(`📝 Creating topic: ${topicData.name}`);
  await topicManager.createTopic(topicData.name, topicData.code, topicData.description);
  
  console.log('🎉 Topic creation completed successfully!');
  
  // Optional: Take a screenshot of the final state
  await page.screenshot({ path: 'topic-created-success.png', fullPage: true });
});
