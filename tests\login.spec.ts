import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import testData from '../test-data/test-data.json';
import envConfig from '../env/dev.json';

test.describe('Login Functionality', () => {
  
  test('Successful login with valid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Perform login
    await loginPage.loginWithValidUser();
    
    // Verify successful login - check that we're no longer on the login page
    await expect(page).not.toHaveURL(envConfig.baseUrl);

    // Verify we're redirected to a logged-in page (could be dashboard, session check, etc.)
    expect(page.url()).toContain('cqmprdtstapp01.aurelius.com');
  });

 
});