import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import testData from '../test-data/test-data.json';
import envConfig from '../env/dev.json';

test.describe('Login Functionality', () => {
  
  test('Successful login with valid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Perform login
    await loginPage.loginWithValidUser();
    
    // Verify successful login
    await loginPage.waitForSuccessfulLogin();
    const expectedUrl = envConfig.baseUrl + testData.urls.dashboard;
    await expect(page).toHaveURL(expectedUrl);
  });

  test('Failed login with invalid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Attempt login with invalid credentials
    await loginPage.loginWithInvalidUser();
    
    // Verify error message appears
    await expect(loginPage.isErrorMessageVisible()).resolves.toBe(true);
    const errorText = await loginPage.getErrorMessage();
    expect(errorText).toContain(testData.messages.invalidCredentials);
  });

  test('Failed login with empty credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Attempt login with empty credentials
    await loginPage.loginWithEmptyCredentials();
    
    // Verify validation message
    await expect(loginPage.isErrorMessageVisible()).resolves.toBe(true);
  });

  test('Login with custom credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Login with custom data
    await loginPage.loginWithCustomCredentials('<EMAIL>', 'custompass');
    
    // Add your verification logic here
  });
});