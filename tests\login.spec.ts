import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import testData from '../test-data/test-data.json';
import envConfig from '../env/dev.json';

test.describe('Login Functionality', () => {
  
  test('Successful login with valid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // Navigate to login page
    await loginPage.navigate();
    
    // Perform login
    await loginPage.loginWithValidUser();
    
    // Verify successful login
    await loginPage.waitForSuccessfulLogin();
    const expectedUrl = envConfig.baseUrl + testData.urls.dashboard;
    await expect(page).toHaveURL(expectedUrl);
  });

 
});