{"topics": {"validTopic": {"name": "AutomationTopic_2025", "uniqueCode": "AUTO_TOPIC_001", "description": "This is a test topic created through automation for validation purposes"}, "testTopic": {"name": "TestTopic_QA", "uniqueCode": "TEST_QA_002", "description": "Quality assurance test topic for regression testing"}, "sampleTopic": {"name": "SampleTopic_Demo", "uniqueCode": "SAMPLE_DEMO_003", "description": "Sample topic for demonstration and training purposes"}, "trainingTopic": {"name": "TrainingTopic_Course", "uniqueCode": "TRAIN_COURSE_004", "description": "Training topic for course management and learning modules"}, "productionTopic": {"name": "ProductionTopic_Live", "uniqueCode": "PROD_LIVE_005", "description": "Production topic for live environment testing"}}, "topicTemplates": {"namePrefix": "Topic_", "codePrefix": "TC_", "defaultDescription": "Automated topic created for testing", "suffixes": {"automation": "_AUTO", "manual": "_MANUAL", "regression": "_REGR", "smoke": "_SMOKE", "integration": "_INTEG"}}, "topicCategories": {"courseManagement": {"namePrefix": "CM_", "codePrefix": "COURSE_", "description": "Course management related topics"}, "userTraining": {"namePrefix": "UT_", "codePrefix": "TRAIN_", "description": "User training and education topics"}, "systemTesting": {"namePrefix": "ST_", "codePrefix": "SYS_", "description": "System testing and validation topics"}}, "documents": {"recentDocument": "Document 14 March", "alternativeDocuments": ["Document 15 March", "Document 16 March", "Training Manual v2.1", "User Guide 2025"]}, "radioOptions": {"defaultSelection": 1, "availableOptions": ["Option 1", "Option 2", "Option 3"]}, "validation": {"maxNameLength": 100, "maxCodeLength": 50, "maxDescriptionLength": 500, "requiredFields": ["name", "uniqueCode", "description"], "codePattern": "^[A-Z0-9_]+$", "namePattern": "^[A-Za-z0-9_\\s]+$"}, "messages": {"topicCreated": "Topic created successfully", "topicExists": "Topic with this code already exists", "invalidCode": "Invalid topic code format", "missingFields": "Please fill all required fields"}}