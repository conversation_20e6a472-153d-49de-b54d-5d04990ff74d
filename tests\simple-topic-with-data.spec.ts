import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import { TopicManagerPage } from '../src/TopicManagerPage';

test('Quick Topic Creation with Test Data', async ({ page }) => {
  // Initialize page objects
  const loginPage = new LoginPage(page);
  const topicManager = new TopicManagerPage(page);
  
  console.log('🚀 Starting quick topic creation with test data...');
  
  // Step 1: Login
  console.log('🔐 Logging in...');
  await loginPage.navigate();
  await loginPage.loginWithValidUser();
  await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
  console.log('✅ Login successful!');
  
  // Step 2: Create topic using test data (much simpler now!)
  console.log('📝 Creating topic using predefined test data...');
  const createdTopic = await topicManager.createTopicWithTestData();
  
  console.log('🎉 Topic creation completed!');
  console.log('📋 Created topic details:');
  console.log(`   Name: ${createdTopic.name}`);
  console.log(`   Code: ${createdTopic.uniqueCode}`);
  console.log(`   Description: ${createdTopic.description}`);
  
  // Optional: Take a screenshot
  await page.screenshot({ path: 'topic-created-with-data.png', fullPage: true });
});
