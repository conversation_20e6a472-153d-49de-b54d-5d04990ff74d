import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';

test('Debug login page', async ({ page }) => {
  const loginPage = new LoginPage(page);
  
  // Navigate to login page
  await loginPage.navigate();
  
  // Take a screenshot to see the page
  await page.screenshot({ path: 'debug-login-page.png', fullPage: true });
  
  // Wait a bit to see the page
  await page.waitForTimeout(2000);
  
  // Check what form fields exist
  const formFields = await page.locator('input, select, textarea').all();
  console.log('Found form fields:', formFields.length);
  
  for (let i = 0; i < formFields.length; i++) {
    const field = formFields[i];
    const tagName = await field.evaluate(el => el.tagName);
    const id = await field.getAttribute('id');
    const name = await field.getAttribute('name');
    const type = await field.getAttribute('type');
    const placeholder = await field.getAttribute('placeholder');
    
    console.log(`Field ${i + 1}: ${tagName} - id: ${id}, name: ${name}, type: ${type}, placeholder: ${placeholder}`);
  }
  
  // Check the login button
  const loginButton = await page.locator('#btnSubmit');
  const isDisabled = await loginButton.getAttribute('disabled');
  const buttonText = await loginButton.textContent();
  console.log(`Login button - disabled: ${isDisabled}, text: ${buttonText}`);
  
  // Try filling the fields we know about
  console.log('Filling company field...');
  await page.fill('#company', 'epiqsp3');
  await page.waitForTimeout(500);
  
  console.log('Filling username field...');
  await page.fill('#username', 'epiqsp3');
  await page.waitForTimeout(500);
  
  console.log('Filling password field...');
  await page.fill('#password', 'YourActualPassword');
  await page.waitForTimeout(500);
  
  // Check button status after filling
  const isDisabledAfter = await loginButton.getAttribute('disabled');
  console.log(`Login button after filling - disabled: ${isDisabledAfter}`);
  
  // Take another screenshot
  await page.screenshot({ path: 'debug-login-filled.png', fullPage: true });
  
  // Wait to see if button becomes enabled
  await page.waitForTimeout(2000);
  
  const isDisabledFinal = await loginButton.getAttribute('disabled');
  console.log(`Login button final - disabled: ${isDisabledFinal}`);
});
