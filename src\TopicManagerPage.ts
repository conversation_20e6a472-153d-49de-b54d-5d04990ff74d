import { Page, FrameLocator } from '@playwright/test';
import topicData from '../test-data/topic-data.json';

export class TopicManagerPage {
  readonly page: Page;
  private bodyFrame: FrameLocator;

  // Main page selectors
  private readonly plantSelector = 'text=SP3 Plant ( SPP1 )';
  private readonly homeIcon = 'link[name="🌅"]';
  private readonly courseManagerLink = 'link[name="Course Manager"]';
  private readonly initiateLink = 'link[name="Initiate"]';
  private readonly topicLink = 'link[name="Topic"]';
  private readonly logoutLink = 'link[name=""]';
  private readonly yesButton = 'button[name="Yes"]';

  // Frame selectors (inside bodyFrame iframe)
  private readonly topicNameField = 'textbox[name="Topic Name"]';
  private readonly topicUniqueCodeField = 'textbox[name="Topic Unique Code"]';
  private readonly descriptionField = 'textbox[name="Description"]';
  private readonly radioButton = 'radio';
  private readonly searchTextbox = '#CMTopic_TreeVC_SearchTxt';
  private readonly recentDocumentsLink = 'link[name="Recent 10 Documents"]';
  private readonly documentLink = 'text=Document 14 March';
  private readonly submitButton = '#btnSubmit_1';
  private readonly topicRegistrationText = 'text=TopicRegistration';
  private readonly doneLink = 'link[name="Done"]';

  constructor(page: Page) {
    this.page = page;
    this.bodyFrame = this.page.locator('iframe[name="bodyFrame"]').contentFrame();
  }

  // Navigation methods
  async selectPlant() {
    await this.page.getByText('SP3 Plant ( SPP1 )').click();
  }

  async clickHomeIcon() {
    await this.page.getByRole('link', { name: '🌅' }).click();
  }

  async navigateToCourseManager() {
    await this.page.getByRole('link', { name: 'Course Manager' }).click();
  }

  async clickCourseManagerSection() {
    await this.page.getByText('Course ManagerInitiateTopic').click();
    await this.page.getByText('Course ManagerInitiateTopic').click();
  }

  async navigateToInitiate() {
    await this.page.getByRole('link', { name: 'Initiate' }).click();
  }

  async navigateToTopic() {
    await this.page.getByRole('link', { name: 'Topic' }).click();
  }

  // Topic creation methods (working inside iframe)
  async fillTopicName(topicName: string) {
    await this.bodyFrame.getByRole('textbox', { name: 'Topic Name' }).click();
    await this.bodyFrame.getByRole('textbox', { name: 'Topic Name' }).fill(topicName);
  }

  async fillTopicUniqueCode(uniqueCode: string) {
    await this.bodyFrame.getByRole('textbox', { name: 'Topic Unique Code' }).click();
    await this.bodyFrame.getByRole('textbox', { name: 'Topic Unique Code' }).fill(uniqueCode);
  }

  async fillDescription(description: string) {
    await this.bodyFrame.getByRole('textbox', { name: 'Description' }).click();
    await this.bodyFrame.getByRole('textbox', { name: 'Description' }).fill(description);
  }

  async selectRadioOption() {
    await this.bodyFrame.getByRole('radio').nth(1).check();
  }

  async interactWithSearchBox() {
    await this.bodyFrame.locator('#CMTopic_TreeVC_SearchTxt').click();
    await this.bodyFrame.locator('#CMTopic_TreeVC_SearchTxt').press('ArrowLeft');
  }

  async selectRecentDocuments() {
    await this.bodyFrame.getByRole('link', { name: 'Recent 10 Documents' }).click();
  }

  async selectDocument() {
    await this.bodyFrame.getByText('Document 14 March').click();
  }

  async submitTopic() {
    await this.bodyFrame.locator('#btnSubmit_1').click();
  }

  async verifyTopicRegistration() {
    await this.bodyFrame.getByText('TopicRegistration').click();
  }

  async clickDone() {
    await this.bodyFrame.getByRole('link', { name: 'Done' }).click();
  }

  async logout() {
    await this.page.getByRole('link', { name: '' }).click();
  }

  async handleSessionConfirmation() {
    await this.page.getByRole('button', { name: 'Yes' }).click();
  }

  // Combined workflow methods
  async navigateToTopicCreation() {
    console.log('🏭 Selecting plant...');
    await this.selectPlant();
    
    console.log('🏠 Clicking home icon...');
    await this.clickHomeIcon();
    
    console.log('📚 Navigating to Course Manager...');
    await this.navigateToCourseManager();
    
    console.log('📋 Clicking Course Manager section...');
    await this.clickCourseManagerSection();
    
    console.log('🚀 Navigating to Initiate...');
    await this.navigateToInitiate();
    
    console.log('📝 Navigating to Topic...');
    await this.navigateToTopic();
  }

  async createTopic(topicName: string, uniqueCode: string, description: string) {
    console.log('📝 Filling topic details...');
    
    // Fill topic information
    await this.fillTopicName(topicName);
    await this.fillTopicUniqueCode(uniqueCode);
    await this.fillDescription(description);
    
    // Select radio option
    await this.selectRadioOption();
    
    // Interact with search and documents
    await this.interactWithSearchBox();
    await this.selectRecentDocuments();
    await this.selectDocument();
    
    // Submit the topic
    console.log('✅ Submitting topic...');
    await this.submitTopic();
    
    // Verify and complete
    await this.verifyTopicRegistration();
    await this.clickDone();
    
    console.log('🎉 Topic created successfully!');
  }

  async createTopicComplete(topicName: string, uniqueCode: string, description: string) {
    // Handle session confirmation if needed
    try {
      await this.handleSessionConfirmation();
    } catch (error) {
      console.log('ℹ️ No session confirmation needed');
    }

    // Navigate to topic creation
    await this.navigateToTopicCreation();

    // Create the topic
    await this.createTopic(topicName, uniqueCode, description);

    // Logout
    console.log('🚪 Logging out...');
    await this.logout();
  }

  // Topic data utility methods
  generateUniqueTopicData(baseTopic: keyof typeof topicData.topics = 'validTopic') {
    const timestamp = Date.now();
    const baseData = topicData.topics[baseTopic];

    return {
      name: `${baseData.name}_${timestamp}`,
      uniqueCode: `${baseData.uniqueCode}_${timestamp}`,
      description: `${baseData.description} - Generated at ${new Date().toLocaleString()}`
    };
  }

  generateTopicFromTemplate(category: keyof typeof topicData.topicCategories = 'courseManagement', suffix: string = '') {
    const timestamp = Date.now();
    const categoryData = topicData.topicCategories[category];
    const templates = topicData.topicTemplates;

    return {
      name: `${categoryData.namePrefix}${templates.namePrefix}${timestamp}${suffix}`,
      uniqueCode: `${categoryData.codePrefix}${timestamp}${suffix}`,
      description: `${categoryData.description} - ${templates.defaultDescription} - ${new Date().toLocaleString()}`
    };
  }

  generateTopicWithSuffix(baseTopic: keyof typeof topicData.topics, suffixType: keyof typeof topicData.topicTemplates.suffixes) {
    const timestamp = Date.now();
    const baseData = topicData.topics[baseTopic];
    const suffix = topicData.topicTemplates.suffixes[suffixType];

    return {
      name: `${baseData.name}${suffix}_${timestamp}`,
      uniqueCode: `${baseData.uniqueCode}${suffix}_${timestamp}`,
      description: `${baseData.description} - ${suffixType} testing - ${new Date().toLocaleString()}`
    };
  }

  async createTopicWithTestData(topicType: keyof typeof topicData.topics = 'validTopic') {
    const topicInfo = this.generateUniqueTopicData(topicType);
    console.log(`📝 Creating topic from test data: ${topicInfo.name}`);

    await this.createTopicComplete(topicInfo.name, topicInfo.uniqueCode, topicInfo.description);
    return topicInfo;
  }

  async createTopicWithCategory(category: keyof typeof topicData.topicCategories = 'courseManagement', suffix: string = '') {
    const topicInfo = this.generateTopicFromTemplate(category, suffix);
    console.log(`📝 Creating topic from category template: ${topicInfo.name}`);

    await this.createTopicComplete(topicInfo.name, topicInfo.uniqueCode, topicInfo.description);
    return topicInfo;
  }

  async createTopicWithSuffixType(baseTopic: keyof typeof topicData.topics, suffixType: keyof typeof topicData.topicTemplates.suffixes) {
    const topicInfo = this.generateTopicWithSuffix(baseTopic, suffixType);
    console.log(`📝 Creating topic with suffix: ${topicInfo.name}`);

    await this.createTopicComplete(topicInfo.name, topicInfo.uniqueCode, topicInfo.description);
    return topicInfo;
  }
}
