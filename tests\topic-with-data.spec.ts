import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';
import { TopicManagerPage } from '../src/TopicManagerPage';
import topicData from '../test-data/topic-data.json';

test.describe('Topic Creation with Test Data', () => {
  
  test('Create topic using predefined test data - validTopic', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);
    
    console.log('🔐 Logging in...');
    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    
    console.log('📝 Creating topic using validTopic data...');
    const createdTopic = await topicManager.createTopicWithTestData('validTopic');
    
    console.log('✅ Topic created:', createdTopic);
  });

  test('Create topic using testTopic data', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);
    
    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    
    const createdTopic = await topicManager.createTopicWithTestData('testTopic');
    console.log('✅ Test topic created:', createdTopic);
  });

  test('Create topic using sampleTopic data', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const topicManager = new TopicManagerPage(page);

    await loginPage.navigate();
    await loginPage.loginWithValidUser();
    await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');

    const createdTopic = await topicManager.createTopicWithTestData('sampleTopic');
    console.log('✅ Sample topic created:', createdTopic);
  });

  test('Display all available topic data options', async ({ page }) => {
    console.log('\n📋 Available Topic Data Options:');
    
    console.log('\n🎯 Predefined Topics:');
    Object.keys(topicData.topics).forEach(key => {
      const topic = topicData.topics[key as keyof typeof topicData.topics];
      console.log(`  - ${key}: ${topic.name} (${topic.uniqueCode})`);
    });
    
    console.log('\n📂 Topic Categories:');
    Object.keys(topicData.topicCategories).forEach(key => {
      const category = topicData.topicCategories[key as keyof typeof topicData.topicCategories];
      console.log(`  - ${key}: ${category.description}`);
    });
    
    console.log('\n🏷️ Available Suffixes:');
    Object.keys(topicData.topicTemplates.suffixes).forEach(key => {
      const suffix = topicData.topicTemplates.suffixes[key as keyof typeof topicData.topicTemplates.suffixes];
      console.log(`  - ${key}: ${suffix}`);
    });
    
    console.log('\n📄 Available Documents:');
    console.log(`  - Recent: ${topicData.documents.recentDocument}`);
    topicData.documents.alternativeDocuments.forEach(doc => {
      console.log(`  - Alternative: ${doc}`);
    });
    
    // This test just displays information, no actual automation
    expect(true).toBe(true);
  });
});
