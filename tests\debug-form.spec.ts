import { test } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';

test('Debug why login button is not enabling', async ({ page }) => {
  const loginPage = new LoginPage(page);
  
  // Navigate to login page
  await loginPage.navigate();
  
  // Take initial screenshot
  await page.screenshot({ path: 'debug-1-initial.png', fullPage: true });
  
  console.log('=== DEBUGGING LOGIN BUTTON ISSUE ===');
  
  // Check initial button state
  const loginButton = page.locator('#btnSubmit');
  console.log('Initial button disabled:', await loginButton.getAttribute('disabled'));
  
  // Check what form fields actually exist on the page
  console.log('\n=== FORM FIELDS ON PAGE ===');
  const allInputs = await page.locator('input, select, textarea').all();
  
  for (let i = 0; i < allInputs.length; i++) {
    const input = allInputs[i];
    const id = await input.getAttribute('id') || 'no-id';
    const name = await input.getAttribute('name') || 'no-name';
    const type = await input.getAttribute('type') || 'no-type';
    const required = await input.getAttribute('required');
    const placeholder = await input.getAttribute('placeholder') || 'no-placeholder';
    
    console.log(`Field ${i + 1}: id="${id}", name="${name}", type="${type}", required="${required}", placeholder="${placeholder}"`);
  }
  
  // Try to find the actual field selectors
  console.log('\n=== CHECKING FIELD SELECTORS ===');
  
  // Check if our assumed selectors exist
  const companyField = page.locator('#company');
  const usernameField = page.locator('#username');
  const passwordField = page.locator('#password');
  
  console.log('Company field exists:', await companyField.count() > 0);
  console.log('Username field exists:', await usernameField.count() > 0);
  console.log('Password field exists:', await passwordField.count() > 0);
  
  // If they don't exist, try to find them by other attributes
  if (await companyField.count() === 0) {
    const possibleCompany = await page.locator('input[name*="company"], input[id*="company"], input[placeholder*="company"]').all();
    console.log('Possible company fields found:', possibleCompany.length);
    for (let i = 0; i < possibleCompany.length; i++) {
      const field = possibleCompany[i];
      const id = await field.getAttribute('id');
      const name = await field.getAttribute('name');
      console.log(`  Company candidate ${i + 1}: id="${id}", name="${name}"`);
    }
  }
  
  if (await usernameField.count() === 0) {
    const possibleUsername = await page.locator('input[name*="user"], input[id*="user"], input[type="text"], input[placeholder*="user"]').all();
    console.log('Possible username fields found:', possibleUsername.length);
    for (let i = 0; i < possibleUsername.length; i++) {
      const field = possibleUsername[i];
      const id = await field.getAttribute('id');
      const name = await field.getAttribute('name');
      console.log(`  Username candidate ${i + 1}: id="${id}", name="${name}"`);
    }
  }
  
  if (await passwordField.count() === 0) {
    const possiblePassword = await page.locator('input[type="password"]').all();
    console.log('Possible password fields found:', possiblePassword.length);
    for (let i = 0; i < possiblePassword.length; i++) {
      const field = possiblePassword[i];
      const id = await field.getAttribute('id');
      const name = await field.getAttribute('name');
      console.log(`  Password candidate ${i + 1}: id="${id}", name="${name}"`);
    }
  }
  
  // Try filling with our current selectors
  console.log('\n=== TRYING TO FILL FIELDS ===');
  
  try {
    await page.fill('#company', 'epiqsp3');
    console.log('✓ Company field filled');
  } catch (error) {
    console.log('✗ Company field failed:', error.message);
  }
  
  try {
    await page.fill('#username', 'dawnkx2y');
    console.log('✓ Username field filled');
  } catch (error) {
    console.log('✗ Username field failed:', error.message);
  }
  
  try {
    await page.fill('#password', 'password3');
    console.log('✓ Password field filled');
  } catch (error) {
    console.log('✗ Password field failed:', error.message);
  }
  
  // Wait a moment for any JavaScript validation
  await page.waitForTimeout(1000);
  
  // Check button state after filling
  console.log('\n=== BUTTON STATE AFTER FILLING ===');
  console.log('Button disabled after filling:', await loginButton.getAttribute('disabled'));
  
  // Take screenshot after filling
  await page.screenshot({ path: 'debug-2-after-filling.png', fullPage: true });
  
  // Check for any validation messages
  console.log('\n=== CHECKING FOR VALIDATION MESSAGES ===');
  const errorMessages = await page.locator('.error, .validation-error, .invalid, [class*="error"]').all();
  console.log('Error messages found:', errorMessages.length);
  
  for (let i = 0; i < errorMessages.length; i++) {
    const msg = errorMessages[i];
    const text = await msg.textContent();
    const isVisible = await msg.isVisible();
    console.log(`Error ${i + 1}: "${text}" (visible: ${isVisible})`);
  }
  
  // Check if there are any required fields not filled
  console.log('\n=== CHECKING REQUIRED FIELDS ===');
  const requiredFields = await page.locator('input[required], select[required], textarea[required]').all();
  console.log('Required fields found:', requiredFields.length);
  
  for (let i = 0; i < requiredFields.length; i++) {
    const field = requiredFields[i];
    const id = await field.getAttribute('id');
    const name = await field.getAttribute('name');
    const value = await field.inputValue();
    const isEmpty = value === '';
    console.log(`Required field ${i + 1}: id="${id}", name="${name}", empty: ${isEmpty}`);
  }
  
  console.log('\n=== DEBUG COMPLETE ===');
});
