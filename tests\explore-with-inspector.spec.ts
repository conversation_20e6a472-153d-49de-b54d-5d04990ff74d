import { test, expect } from '@playwright/test';
import { LoginPage } from '../src/LoginPage';

test('Login and explore with Playwright Inspector', async ({ page }) => {
  const loginPage = new LoginPage(page);
  
  // Perform login first
  console.log('🔐 Logging in...');
  await loginPage.navigate();
  await loginPage.loginWithValidUser();
  
  // Verify we're logged in
  await expect(page).not.toHaveURL('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
  console.log('✅ Login successful!');
  
  // Take a screenshot of the logged-in state
  await page.screenshot({ path: 'logged-in-state.png', fullPage: true });
  
  // Open Playwright Inspector to explore the application
  console.log('🔍 Opening Playwright Inspector...');
  console.log('📝 You can now:');
  console.log('   - Click elements to see their selectors');
  console.log('   - Record new actions');
  console.log('   - Inspect the page structure');
  console.log('   - Generate code for new interactions');
  
  // Pause the test to open inspector
  await page.pause();
  
  // After you're done exploring, the test will continue
  console.log('🎯 Inspector session completed!');
});
